package models

import (
	"log"

	"github.com/gobuffalo/envy"
	"github.com/gobuffalo/pop/v6"
	_ "github.com/jackc/pgx/v4/stdlib" // PostgreSQL driver
)

// DB is a connection to your database to be used
// throughout your application.
var DB *pop.Connection

func init() {
	var err error
	env := envy.Get("GO_ENV", "development")
	log.Printf("Attempting to connect to database with environment: %s", env)
	log.Printf("Current working directory: %s", envy.Get("PWD", "unknown"))

	// Temporarily skip database connection for testing
	log.Printf("Skipping database connection for now...")
	return

	DB, err = pop.Connect(env)
	if err != nil {
		log.Printf("Database connection error: %v", err)
		log.Fatal(err)
	}
	log.Printf("Successfully connected to database")
	pop.Debug = env == "development"
}
